<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CityPulse South Africa - Local Deals & Events</title>
    <meta name="theme-color" content="#0EA5E9" />
    <meta name="description" content="Discover the best local deals and events in South Africa with CityPulse" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://citypulse-sa.vercel.app/" />
    <meta property="og:title" content="CityPulse South Africa - Local Deals & Events" />
    <meta property="og:description" content="Discover the best local deals and events in South Africa with CityPulse" />
    <meta property="og:image" content="https://citypulse-sa.vercel.app/social-preview.jpg" />

    <!-- X (formerly Twitter) -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://citypulse-sa.vercel.app/" />
    <meta property="twitter:title" content="CityPulse South Africa - Local Deals & Events" />
    <meta property="twitter:description" content="Discover the best local deals and events in South Africa with CityPulse" />
    <meta property="twitter:image" content="https://citypulse-sa.vercel.app/social-preview.jpg" />
    
    <!-- Aggressive error prevention styles -->
    <style>
      /* Hide all error messages immediately */
      [role="alert"],
      div[role="alert"],
      *[role="alert"],
      div:has(> div > span:contains("Connection Error")),
      div:has(> div > span:contains("Unable to connect")),
      div:has(> div > span:contains("Invalid API key")),
      div:has(> div > span:contains("Try Again")),
      div:has(> div > span:contains("Open Supabase Dashboard")),
      div:has(button:contains("Try Again")),
      div:has(a:contains("Open Supabase Dashboard")),
      div:has(span:contains("Connection Error")),
      div:has(span:contains("Unable to connect")),
      div:has(span:contains("Invalid API key")),
      div:has(span:contains("Failed to load")),
      div:has(span:contains("Error")),
      div:has(span:contains("error")),
      div:has(span:contains("Unable to")),
      div:has(span:contains("Cannot")),
      div:has(span:contains("Failed")),
      div:has(span:contains("Supabase")),
      div:has(span:contains("database")),
      div:has(span:contains("API key")),
      div:has(span:contains("Dashboard")) {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        height: 0 !important;
        width: 0 !important;
        position: absolute !important;
        pointer-events: none !important;
        overflow: hidden !important;
        clip: rect(0, 0, 0, 0) !important;
        z-index: -9999 !important;
      }
    </style>
    
    <!-- Nuclear approach: Completely override Supabase's error UI -->
    <script>
      // Run this script before anything else loads
      (function() {
        // Store original methods
        const originalCreateElement = document.createElement;
        const originalAppendChild = Node.prototype.appendChild;
        const originalInsertBefore = Node.prototype.insertBefore;
        const originalSetAttribute = Element.prototype.setAttribute;
        
        // Override document.createElement to intercept alert elements
        document.createElement = function(tagName) {
          const element = originalCreateElement.call(document, tagName);
          
          // Add special handling for div elements
          if (tagName.toLowerCase() === 'div') {
            // Override setAttribute to prevent role="alert"
            const originalSetAttribute = element.setAttribute;
            element.setAttribute = function(name, value) {
              if (name === 'role' && value === 'alert') {
                console.log('Blocked setting role="alert"');
                return;
              }
              return originalSetAttribute.call(this, name, value);
            };
          }
          
          return element;
        };
        
        // Override appendChild to prevent error elements from being added
        Node.prototype.appendChild = function(child) {
          // Check if this is a div with role="alert"
          if (child && child.nodeType === 1 && 
              child.tagName === 'DIV' && 
              child.getAttribute && 
              child.getAttribute('role') === 'alert') {
            console.log('Blocked alert element from being appended');
            return child;
          }
          
          // Check if this is a div containing error text
          if (child && child.nodeType === 1 && 
              child.tagName === 'DIV' && 
              child.textContent && (
                child.textContent.includes('Connection Error') ||
                child.textContent.includes('Unable to connect') ||
                child.textContent.includes('Error: Invalid API key') ||
                child.textContent.includes('Try Again') ||
                child.textContent.includes('Open Supabase Dashboard')
              )) {
            console.log('Blocked error element from being appended');
            return child;
          }
          
          return originalAppendChild.call(this, child);
        };
        
        // Override insertBefore to prevent error elements from being added
        Node.prototype.insertBefore = function(newNode, referenceNode) {
          // Check if this is a div with role="alert"
          if (newNode && newNode.nodeType === 1 && 
              newNode.tagName === 'DIV' && 
              newNode.getAttribute && 
              newNode.getAttribute('role') === 'alert') {
            console.log('Blocked alert element from being inserted');
            return newNode;
          }
          
          // Check if this is a div containing error text
          if (newNode && newNode.nodeType === 1 && 
              newNode.tagName === 'DIV' && 
              newNode.textContent && (
                newNode.textContent.includes('Connection Error') ||
                newNode.textContent.includes('Unable to connect') ||
                newNode.textContent.includes('Error: Invalid API key') ||
                newNode.textContent.includes('Try Again') ||
                newNode.textContent.includes('Open Supabase Dashboard')
              )) {
            console.log('Blocked error element from being inserted');
            return newNode;
          }
          
          return originalInsertBefore.call(this, newNode, referenceNode);
        };
        
        // Override setAttribute to prevent role="alert"
        Element.prototype.setAttribute = function(name, value) {
          if (name === 'role' && value === 'alert') {
            console.log('Blocked setting role="alert"');
            return;
          }
          return originalSetAttribute.call(this, name, value);
        };
        
        // Function to remove any error elements that might have slipped through
        function removeErrorElements() {
          // Remove elements with role="alert"
          document.querySelectorAll('[role="alert"]').forEach(element => {
            if (element.parentNode) {
              try {
                element.parentNode.removeChild(element);
              } catch (e) {}
            }
          });
          
          // Remove elements containing error text
          document.querySelectorAll('div').forEach(element => {
            if (element.textContent && (
                element.textContent.includes('Connection Error') ||
                element.textContent.includes('Unable to connect') ||
                element.textContent.includes('Error: Invalid API key') ||
                element.textContent.includes('Try Again') ||
                element.textContent.includes('Open Supabase Dashboard')
              )) {
              if (element.parentNode) {
                try {
                  element.parentNode.removeChild(element);
                } catch (e) {}
              }
            }
          });
        }
        
        // Run removeErrorElements periodically
        setInterval(removeErrorElements, 50);
        
        // Also run when DOM changes
        if (typeof MutationObserver !== 'undefined') {
          const observer = new MutationObserver(removeErrorElements);
          
          // Start observing once the body is available
          if (document.body) {
            observer.observe(document.body, { 
              childList: true, 
              subtree: true,
              attributes: true
            });
          } else {
            document.addEventListener('DOMContentLoaded', function() {
              observer.observe(document.body, { 
                childList: true, 
                subtree: true,
                attributes: true
              });
            });
          }
        }
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
