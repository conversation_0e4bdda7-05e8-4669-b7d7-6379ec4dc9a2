<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="192" height="192" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Colorful gradient background -->
  <rect width="512" height="512" rx="100" fill="url(#gradient)" />

  <!-- C letter using path -->
  <path d="M180 336C156 336 136 328 120 312C104 296 96 274.667 96 248C96 221.333 104 200 120 184C136 168 156 160 180 160C196 160 210 164 222 172C234 180 242.667 190.667 248 204L208 220C205.333 212 201.333 206 196 202C190.667 198 184 196 176 196C162.667 196 152 200.667 144 210C136 219.333 132 232 132 248C132 264 136 276.667 144 286C152 295.333 162.667 300 176 300C184 300 190.667 298 196 294C201.333 290 205.333 284 208 276L248 292C242.667 305.333 234 316 222 324C210 332 196 336 180 336Z" fill="white"/>

  <!-- P letter using path -->
  <path d="M288 336V160H368C384 160 396.667 164.667 406 174C415.333 183.333 420 195.333 420 210C420 224.667 415.333 236.667 406 246C396.667 255.333 384 260 368 260H320V336H288ZM320 228H364C370.667 228 376 226 380 222C384 218 386 214.667 386 210C386 205.333 384 202 380 198C376 194 370.667 192 364 192H320V228Z" fill="white"/>

  <defs>
    <!-- Vibrant gradient background -->
    <linearGradient id="gradient" x1="0" y1="0" x2="512" y2="512" gradientUnits="userSpaceOnUse">
      <stop stop-color="#0EA5E9" />
      <stop offset="0.33" stop-color="#8B5CF6" />
      <stop offset="0.66" stop-color="#EC4899" />
      <stop offset="1" stop-color="#F59E0B" />
    </linearGradient>
  </defs>
</svg>
