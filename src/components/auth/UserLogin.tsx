import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, Mail, Lock, CheckCircle, XCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';
import { isValidEmail, isValidPassword, checkPasswordStrength } from '@/lib/validation';

interface UserLoginProps {
  onClose?: () => void;
}

const UserLogin = ({ onClose }: UserLoginProps) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [registerEmail, setRegisterEmail] = useState("");
  const [registerPassword, setRegisterPassword] = useState("");
  const [formError, setFormError] = useState("");
  const [activeTab, setActiveTab] = useState("login");

  const { signIn, signUp, signInWithGoogle, signInWithFacebook, loading, resetPassword } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError("");

    // Basic validation
    if (!email) {
      setFormError("Please enter your email address");
      return;
    }

    if (!password) {
      setFormError("Please enter your password");
      return;
    }

    // Email format validation
    if (!isValidEmail(email)) {
      setFormError("Please enter a valid email address");
      return;
    }

    try {
      await signIn(email, password);
      toast.success("Signed in successfully");
      if (onClose) onClose();
    } catch (error: any) {
      // Set a user-friendly error message
      if (error?.message?.includes("Invalid login credentials")) {
        setFormError("Invalid email or password. Please try again.");
      } else if (error?.message?.includes("Email not confirmed")) {
        setFormError("Please verify your email address before logging in.");
        toast.info("Email verification required", {
          description: "Check your inbox for a verification email or request a new one."
        });
      } else {
        setFormError("An error occurred during login. Please try again.");
      }
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError("");

    // Basic validation
    if (!registerEmail) {
      setFormError("Please enter your email address");
      return;
    }

    if (!registerPassword) {
      setFormError("Please enter a password");
      return;
    }

    // Email format validation
    if (!isValidEmail(registerEmail)) {
      setFormError("Please enter a valid email address");
      return;
    }

    // Password strength validation
    const passwordStrength = checkPasswordStrength(registerPassword);

    if (passwordStrength.score < 3) {
      setFormError(`Password is too weak. ${passwordStrength.feedback}`);
      return;
    }

    try {
      await signUp(registerEmail, registerPassword);

      // Clear form
      setRegisterEmail("");
      setRegisterPassword("");

      // Switch to login tab
      setActiveTab("login");

      toast.success("Registration successful", {
        description: "Please check your email to verify your account before logging in."
      });
    } catch (error: any) {
      // Set a user-friendly error message
      if (error?.message?.includes("already registered")) {
        setFormError("This email is already registered. Please try login instead.");

        // Suggest to switch to login tab
        toast.info("Account exists", {
          description: "Would you like to sign in instead?",
          action: {
            label: "Sign In",
            onClick: () => {
              setEmail(registerEmail);
              setActiveTab("login");
            }
          }
        });
      } else if (error?.message?.includes("weak password")) {
        setFormError("Please use a stronger password with at least 8 characters, including numbers and special characters.");
      } else {
        setFormError("Registration failed. Please try again.");
      }
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      await signInWithGoogle();
      // No need to close modal here as the page will redirect
    } catch (error) {
      // Error is handled in the auth context
    }
  };

  const handleFacebookSignIn = async () => {
    try {
      await signInWithFacebook();
      // No need to close modal here as the page will redirect
    } catch (error) {
      // Error is handled in the auth context
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="login">Login</TabsTrigger>
          <TabsTrigger value="register">Register</TabsTrigger>
        </TabsList>

        <TabsContent value="login">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">Welcome Back</CardTitle>
              <CardDescription>
                Sign in to your account to save your favorite deals and events.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                {formError && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{formError}</AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="email">Email address</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={loading}
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="password">Password</Label>
                    <button
                      type="button"
                      onClick={() => toast.info("Reset password", {
                        description: "Enter your email above and click 'Reset Password'",
                        action: {
                          label: "Reset Password",
                          onClick: () => {
                            if (!email) {
                              toast.error("Please enter your email address first");
                              return;
                            }
                            resetPassword(email).catch(error => {
                              console.error("Password reset error:", error);
                            });
                          }
                        }
                      })}
                      className="text-sm text-primary hover:underline"
                    >
                      Forgot password?
                    </button>
                  </div>
                  <Input
                    id="password"
                    type="password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={loading}
                  />
                </div>
                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? "Signing in..." : "Sign in"}
                </Button>
              </form>

              <div className="relative my-4">
                <div className="absolute inset-0 flex items-center">
                  <Separator />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">
                    Or continue with
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  type="button"
                  onClick={handleGoogleSignIn}
                  disabled={loading}
                >
                  <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                    <path
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      fill="#4285F4"
                    />
                    <path
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      fill="#34A853"
                    />
                    <path
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      fill="#FBBC05"
                    />
                    <path
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      fill="#EA4335"
                    />
                  </svg>
                  Google
                </Button>
                <Button
                  variant="outline"
                  type="button"
                  onClick={handleFacebookSignIn}
                  disabled={loading}
                >
                  <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                    <path
                      d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                      fill="#1877F2"
                    />
                  </svg>
                  Facebook
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="register">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">Create Account</CardTitle>
              <CardDescription>
                Sign up to get personalized deals and event recommendations.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleRegister} className="space-y-4">
                {formError && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{formError}</AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="register-email">Email address</Label>
                  <Input
                    id="register-email"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                    value={registerEmail}
                    onChange={(e) => setRegisterEmail(e.target.value)}
                    disabled={loading}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="register-password">Password</Label>
                  <Input
                    id="register-password"
                    type="password"
                    required
                    value={registerPassword}
                    onChange={(e) => setRegisterPassword(e.target.value)}
                    disabled={loading}
                  />
                </div>
                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? "Creating account..." : "Create account"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default UserLogin;
