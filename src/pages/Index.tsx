import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from "@/integrations/supabase/client";
import { Button } from '@/components/ui/button';
import { Tag, Calendar, MapPin, Clock, Users } from 'lucide-react';
import { LoadingState } from '@/components/ui/loading-state';
import { handleError } from '@/lib/error-handler';
import { toast } from '@/components/ui/sonner';
import { fallbackDeals, fallbackEvents } from '@/data/fallback-data';
import { EnvWarning } from '@/components/ui/env-warning';

// Use the interfaces from fallback-data.ts
import type { Deal, Event } from '@/data/fallback-data';

// Enhanced card components with beautiful designs
const SimpleDealCard = ({ deal, onClick }: { deal: Deal; onClick: () => void }) => (
  <div
    className="bg-white rounded-xl shadow-md p-6 hover:shadow-xl transition-all duration-300 cursor-pointer border border-gray-100 hover:border-indigo-200 group"
    onClick={onClick}
  >
    <div className="flex items-start justify-between mb-4">
      <div className="flex items-start gap-3">
        <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
          <Tag className="h-6 w-6 text-white" />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-indigo-600 transition-colors">{deal.title}</h3>
          <p className="text-sm text-gray-600 mt-1">{deal.merchant_name}</p>
        </div>
      </div>
      {deal.discount && (
        <span className="bg-gradient-to-r from-red-500 to-pink-500 text-white text-sm font-medium px-3 py-1 rounded-full shadow-sm">
          {deal.discount}
        </span>
      )}
    </div>
    <p className="text-gray-600 text-sm mb-4 line-clamp-2">{deal.description}</p>
    <div className="flex items-center justify-between text-sm">
      <span className="flex items-center gap-1 text-gray-500">
        <MapPin className="h-4 w-4" />
        {deal.category}
      </span>
      <span className="text-indigo-600 font-medium group-hover:text-indigo-700">
        View Deal →
      </span>
    </div>
  </div>
);

const SimpleEventCard = ({ event, onClick }: { event: Event; onClick: () => void }) => (
  <div
    className="bg-white rounded-xl shadow-md p-6 hover:shadow-xl transition-all duration-300 cursor-pointer border border-gray-100 hover:border-green-200 group"
    onClick={onClick}
  >
    <div className="flex items-start gap-3 mb-4">
      <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center flex-shrink-0">
        <Calendar className="h-6 w-6 text-white" />
      </div>
      <div className="flex-1">
        <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-green-600 transition-colors">{event.title}</h3>
        <p className="text-sm text-gray-600 mt-1">{event.location}</p>
      </div>
    </div>
    <p className="text-gray-600 text-sm mb-4 line-clamp-2">{event.description}</p>
    <div className="space-y-2 text-sm text-gray-500 mb-4">
      <div className="flex items-center gap-2">
        <Calendar className="h-4 w-4 text-green-500" />
        <span>{event.date}</span>
        {event.time && (
          <>
            <Clock className="h-4 w-4 text-green-500 ml-2" />
            <span>{event.time}</span>
          </>
        )}
      </div>
      {event.price && (
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-green-500" />
          <span>{event.price}</span>
        </div>
      )}
    </div>
    <div className="flex items-center justify-end">
      <span className="text-green-600 font-medium group-hover:text-green-700">
        View Event →
      </span>
    </div>
  </div>
);

export const Index = () => {
  const [deals, setDeals] = useState<Deal[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  // We're not showing errors anymore, but keeping the state for future use
  const [, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async (retryCount = 0) => {
      try {
        setLoading(true);
        setError(null);

        // Check if Supabase connection is working
        let connectionError = null;
        try {
          // Simple query to check if connection is working - use deals table since we know it exists
          const { error } = await supabase.from('deals').select('id').limit(1);
          connectionError = error;
        } catch (err) {
          console.error("Connection check failed:", err);
          connectionError = err;
        }

        if (connectionError && retryCount < 2) {
          console.log(`Connection attempt ${retryCount + 1} failed, retrying...`);
          setTimeout(() => fetchData(retryCount + 1), 1500); // Retry after 1.5 seconds
          return;
        }

        // Fetch deals with error handling and retry
        let dealsData = null;
        let dealsError = null;

        try {
          const dealsResponse = await supabase
            .from('deals')
            .select('*')
            .order('created_at', { ascending: false })
            .limit(3);

          dealsData = dealsResponse.data;
          dealsError = dealsResponse.error;
        } catch (err) {
          console.error("Error fetching deals:", err);
          dealsError = err;
        }

        if (dealsError) {
          console.warn("Using fallback deals data due to error:", dealsError);
          setDeals(fallbackDeals);
        } else {
          // If we got data but it's empty, use fallback data
          setDeals(dealsData && dealsData.length > 0 ? dealsData : fallbackDeals);
        }

        // Fetch events with error handling and retry
        let eventsData = null;
        let eventsError = null;

        try {
          const eventsResponse = await supabase
            .from('events')
            .select('*')
            .order('date', { ascending: true })
            .limit(3);

          eventsData = eventsResponse.data;
          eventsError = eventsResponse.error;
        } catch (err) {
          console.error("Error fetching events:", err);
          eventsError = err;
        }

        if (eventsError) {
          console.warn("Using fallback events data due to error:", eventsError);
          setEvents(fallbackEvents);
        } else {
          // If we got data but it's empty, use fallback data
          setEvents(eventsData && eventsData.length > 0 ? eventsData : fallbackEvents);
        }

        // Show error message if both requests failed
        if (dealsError && eventsError) {
          if (retryCount >= 2) {
            setError("Failed to load content. Using fallback data instead.");
            toast.error("Connection error", {
              description: "Could not connect to the database. Showing sample data instead."
            });
          } else {
            // Retry one more time
            console.log(`Both requests failed on attempt ${retryCount + 1}, retrying...`);
            setTimeout(() => fetchData(retryCount + 1), 2000); // Retry after 2 seconds
            return;
          }
        } else {
          // Clear any previous error if at least one request succeeded
          setError(null);
        }
      } catch (err) {
        handleError(err, {
          title: "Error loading content",
          message: "An unexpected error occurred. Showing sample data instead."
        });

        if (retryCount < 2) {
          // Retry one more time
          console.log(`Unexpected error on attempt ${retryCount + 1}, retrying...`);
          setTimeout(() => fetchData(retryCount + 1), 2000); // Retry after 2 seconds
          return;
        }

        setError("Failed to load content. Using fallback data instead.");
        // Ensure fallback data is set in case of unexpected errors
        setDeals(fallbackDeals);
        setEvents(fallbackEvents);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const trackPageView = async () => {
      try {
        await supabase.from('analytics').insert({
          event_type: 'page_view',
          event_source: 'home_page',
          source_id: 0, // Adding source_id as required by the schema
          metadata: { page: 'home' }
        });
      } catch (error) {
        // Just log the error but don't show to user since analytics errors are non-critical
        handleError(error, { silent: true });
      }
    };

    // Only track page views if we're not in development mode
    if (import.meta.env.MODE !== 'development') {
      trackPageView();
    }
  }, []);



  const handleDealClick = async (dealId: number) => {
    try {
      await supabase.from('analytics').insert({
        event_type: 'deal_click',
        event_source: 'home_page',
        source_id: dealId,
        metadata: { deal_id: dealId }
      });

      // First get the current views count
      const { data: dealData, error: fetchError } = await supabase
        .from('deals')
        .select('views')
        .eq('id', dealId)
        .single();

      if (fetchError) {
        console.error('Failed to fetch view count:', fetchError);
        return;
      }

      // Update views counter in deals table
      const currentViews = dealData?.views || 0;
      const { error } = await supabase
        .from('deals')
        .update({ views: currentViews + 1 })
        .eq('id', dealId);

      if (error) {
        console.error('Failed to update view count:', error);
      }
    } catch (error) {
      console.error('Failed to track deal click:', error);
    }
  };

  const handleEventClick = async (eventId: number) => {
    try {
      await supabase.from('analytics').insert({
        event_type: 'event_click',
        event_source: 'home_page',
        source_id: eventId,
        metadata: { event_id: eventId }
      });
    } catch (error) {
      console.error('Failed to track event click:', error);
    }
  };

  // Using fallback data imported from @/data/fallback-data

  // Use fallback data if no deals or events are returned from the database
  const displayDeals = deals.length > 0 ? deals : fallbackDeals;
  const displayEvents = events.length > 0 ? events : fallbackEvents;

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="container mx-auto px-4 py-8">
        {/* Environment warning will only show in development mode */}
        <EnvWarning />

        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-4">
            Welcome to <span className="text-indigo-600">CityPulse</span> South Africa
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Discover the best local deals and events across South Africa.
            Connect with your community and never miss out on amazing opportunities.
          </p>

          {/* Hero Visual Mockup */}
          <div className="relative max-w-5xl mx-auto mb-12">
            <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-3xl p-8 shadow-2xl">
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Deals Preview */}
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4">
                    <div className="flex items-center mb-3">
                      <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                        <Tag className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 text-sm">50% Off Burgers</h3>
                        <p className="text-xs text-gray-600">The Burger Joint</p>
                      </div>
                    </div>
                    <div className="text-xs text-blue-600 font-medium">🔥 Featured Deal</div>
                  </div>

                  {/* Events Preview */}
                  <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-4">
                    <div className="flex items-center mb-3">
                      <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                        <Calendar className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 text-sm">Music Festival</h3>
                        <p className="text-xs text-gray-600">Cape Town Stadium</p>
                      </div>
                    </div>
                    <div className="text-xs text-green-600 font-medium">🎵 This Weekend</div>
                  </div>

                  {/* AI Assistant Preview */}
                  <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-4">
                    <div className="flex items-center mb-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                        <span className="text-white text-sm">🤖</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 text-sm">AI PulsePal</h3>
                        <p className="text-xs text-gray-600">Your local guide</p>
                      </div>
                    </div>
                    <div className="text-xs text-purple-600 font-medium">✨ Ask me anything</div>
                  </div>
                </div>

                {/* Stats Row */}
                <div className="grid grid-cols-3 gap-4 mt-6 pt-6 border-t border-gray-100">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-indigo-600">500+</div>
                    <div className="text-xs text-gray-600">Active Deals</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">200+</div>
                    <div className="text-xs text-gray-600">Events Monthly</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">10K+</div>
                    <div className="text-xs text-gray-600">Happy Users</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/deals">
              <Button size="lg" className="w-full sm:w-auto bg-indigo-600 hover:bg-indigo-700">
                <Tag className="mr-2 h-5 w-5" />
                Explore Deals
              </Button>
            </Link>
            <Link to="/events">
              <Button variant="outline" size="lg" className="w-full sm:w-auto border-indigo-600 text-indigo-600 hover:bg-indigo-50">
                <Calendar className="mr-2 h-5 w-5" />
                Find Events
              </Button>
            </Link>
            <Link to="/ai-assistant">
              <Button variant="outline" size="lg" className="w-full sm:w-auto border-purple-600 text-purple-600 hover:bg-purple-50">
                🤖 AI Assistant
              </Button>
            </Link>
          </div>
        </div>

        {/* Featured Deals and Events Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
          <section>
            <h2 className="text-3xl font-bold mb-6 flex items-center gap-2">
              <Tag className="h-6 w-6 text-indigo-600" /> Featured Deals
            </h2>

            <div className="space-y-4">
              {loading ? (
                <LoadingState isLoading={true} type="card" count={3} />
              ) : (
                displayDeals.map(deal => (
                  <SimpleDealCard
                    key={deal.id}
                    deal={deal}
                    onClick={() => {
                      handleDealClick(deal.id);
                      window.location.href = `/deals/${deal.id}`;
                    }}
                  />
                ))
              )}
            </div>

            <div className="mt-6">
              <Link to="/deals">
                <Button variant="outline" size="lg" className="w-full">
                  View All Deals
                </Button>
              </Link>
            </div>
          </section>

          <section>
            <h2 className="text-3xl font-bold mb-6 flex items-center gap-2">
              <Calendar className="h-6 w-6 text-indigo-600" /> Upcoming Events
            </h2>

            <div className="space-y-4">
              {loading ? (
                <LoadingState isLoading={true} type="card" count={3} />
              ) : (
                displayEvents.map(event => (
                  <SimpleEventCard
                    key={event.id}
                    event={event}
                    onClick={() => {
                      handleEventClick(event.id);
                      window.location.href = `/events/${event.id}`;
                    }}
                  />
                ))
              )}
            </div>

            <div className="mt-6">
              <Link to="/events">
                <Button variant="outline" size="lg" className="w-full">
                  View All Events
                </Button>
              </Link>
            </div>
          </section>
        </div>

        {/* Call to Action Section */}
        <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl p-8 text-center text-white">
          <h3 className="text-2xl font-bold mb-4">Ready to explore South Africa?</h3>
          <p className="text-lg mb-6 opacity-90">
            Join thousands of South Africans discovering amazing local experiences every day.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/sign-up">
              <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                Get Started
              </Button>
            </Link>
            <Link to="/ai-assistant">
              <Button size="lg" variant="outline" className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-indigo-600">
                Try AI Assistant
              </Button>
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Index;
