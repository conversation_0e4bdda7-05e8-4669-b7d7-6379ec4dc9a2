import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from "@/integrations/supabase/client";
import { Button } from '@/components/ui/button';
import { Tag, Calendar, MapPin, Star, Clock, Users } from 'lucide-react';
import { LoadingState } from '@/components/ui/loading-state';
import { handleError } from '@/lib/error-handler';
import { toast } from '@/components/ui/sonner';
import { fallbackDeals, fallbackEvents } from '@/data/fallback-data';
import { EnvWarning } from '@/components/ui/env-warning';

// Use the interfaces from fallback-data.ts
import type { Deal, Event } from '@/data/fallback-data';

// Simple card components to replace the complex ones
const SimpleDealCard = ({ deal, onClick }: { deal: Deal; onClick: () => void }) => (
  <div
    className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer"
    onClick={onClick}
  >
    <div className="flex items-start justify-between mb-3">
      <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">{deal.title}</h3>
      {deal.discount && (
        <span className="bg-red-100 text-red-800 text-sm font-medium px-2 py-1 rounded">
          {deal.discount}
        </span>
      )}
    </div>
    <p className="text-gray-600 text-sm mb-3 line-clamp-2">{deal.description}</p>
    <div className="flex items-center justify-between text-sm text-gray-500">
      <span className="flex items-center gap-1">
        <MapPin className="h-4 w-4" />
        {deal.merchant_name}
      </span>
      <span className="flex items-center gap-1">
        <Star className="h-4 w-4" />
        {deal.category}
      </span>
    </div>
  </div>
);

const SimpleEventCard = ({ event, onClick }: { event: Event; onClick: () => void }) => (
  <div
    className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer"
    onClick={onClick}
  >
    <h3 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2">{event.title}</h3>
    <p className="text-gray-600 text-sm mb-3 line-clamp-2">{event.description}</p>
    <div className="space-y-2 text-sm text-gray-500">
      <div className="flex items-center gap-1">
        <Calendar className="h-4 w-4" />
        {event.date}
      </div>
      {event.time && (
        <div className="flex items-center gap-1">
          <Clock className="h-4 w-4" />
          {event.time}
        </div>
      )}
      <div className="flex items-center gap-1">
        <MapPin className="h-4 w-4" />
        {event.location}
      </div>
      {event.price && (
        <div className="flex items-center gap-1">
          <Users className="h-4 w-4" />
          {event.price}
        </div>
      )}
    </div>
  </div>
);

export const Index = () => {
  const [deals, setDeals] = useState<Deal[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  // We're not showing errors anymore, but keeping the state for future use
  const [, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async (retryCount = 0) => {
      try {
        setLoading(true);
        setError(null);

        // Check if Supabase connection is working
        let connectionError = null;
        try {
          // Simple query to check if connection is working - use deals table since we know it exists
          const { error } = await supabase.from('deals').select('id').limit(1);
          connectionError = error;
        } catch (err) {
          console.error("Connection check failed:", err);
          connectionError = err;
        }

        if (connectionError && retryCount < 2) {
          console.log(`Connection attempt ${retryCount + 1} failed, retrying...`);
          setTimeout(() => fetchData(retryCount + 1), 1500); // Retry after 1.5 seconds
          return;
        }

        // Fetch deals with error handling and retry
        let dealsData = null;
        let dealsError = null;

        try {
          const dealsResponse = await supabase
            .from('deals')
            .select('*')
            .order('created_at', { ascending: false })
            .limit(3);

          dealsData = dealsResponse.data;
          dealsError = dealsResponse.error;
        } catch (err) {
          console.error("Error fetching deals:", err);
          dealsError = err;
        }

        if (dealsError) {
          console.warn("Using fallback deals data due to error:", dealsError);
          setDeals(fallbackDeals);
        } else {
          // If we got data but it's empty, use fallback data
          setDeals(dealsData && dealsData.length > 0 ? dealsData : fallbackDeals);
        }

        // Fetch events with error handling and retry
        let eventsData = null;
        let eventsError = null;

        try {
          const eventsResponse = await supabase
            .from('events')
            .select('*')
            .order('date', { ascending: true })
            .limit(3);

          eventsData = eventsResponse.data;
          eventsError = eventsResponse.error;
        } catch (err) {
          console.error("Error fetching events:", err);
          eventsError = err;
        }

        if (eventsError) {
          console.warn("Using fallback events data due to error:", eventsError);
          setEvents(fallbackEvents);
        } else {
          // If we got data but it's empty, use fallback data
          setEvents(eventsData && eventsData.length > 0 ? eventsData : fallbackEvents);
        }

        // Show error message if both requests failed
        if (dealsError && eventsError) {
          if (retryCount >= 2) {
            setError("Failed to load content. Using fallback data instead.");
            toast.error("Connection error", {
              description: "Could not connect to the database. Showing sample data instead."
            });
          } else {
            // Retry one more time
            console.log(`Both requests failed on attempt ${retryCount + 1}, retrying...`);
            setTimeout(() => fetchData(retryCount + 1), 2000); // Retry after 2 seconds
            return;
          }
        } else {
          // Clear any previous error if at least one request succeeded
          setError(null);
        }
      } catch (err) {
        handleError(err, {
          title: "Error loading content",
          message: "An unexpected error occurred. Showing sample data instead."
        });

        if (retryCount < 2) {
          // Retry one more time
          console.log(`Unexpected error on attempt ${retryCount + 1}, retrying...`);
          setTimeout(() => fetchData(retryCount + 1), 2000); // Retry after 2 seconds
          return;
        }

        setError("Failed to load content. Using fallback data instead.");
        // Ensure fallback data is set in case of unexpected errors
        setDeals(fallbackDeals);
        setEvents(fallbackEvents);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const trackPageView = async () => {
      try {
        await supabase.from('analytics').insert({
          event_type: 'page_view',
          event_source: 'home_page',
          source_id: 0, // Adding source_id as required by the schema
          metadata: { page: 'home' }
        });
      } catch (error) {
        // Just log the error but don't show to user since analytics errors are non-critical
        handleError(error, { silent: true });
      }
    };

    // Only track page views if we're not in development mode
    if (import.meta.env.MODE !== 'development') {
      trackPageView();
    }
  }, []);



  const handleDealClick = async (dealId: number) => {
    try {
      await supabase.from('analytics').insert({
        event_type: 'deal_click',
        event_source: 'home_page',
        source_id: dealId,
        metadata: { deal_id: dealId }
      });

      // First get the current views count
      const { data: dealData, error: fetchError } = await supabase
        .from('deals')
        .select('views')
        .eq('id', dealId)
        .single();

      if (fetchError) {
        console.error('Failed to fetch view count:', fetchError);
        return;
      }

      // Update views counter in deals table
      const currentViews = dealData?.views || 0;
      const { error } = await supabase
        .from('deals')
        .update({ views: currentViews + 1 })
        .eq('id', dealId);

      if (error) {
        console.error('Failed to update view count:', error);
      }
    } catch (error) {
      console.error('Failed to track deal click:', error);
    }
  };

  const handleEventClick = async (eventId: number) => {
    try {
      await supabase.from('analytics').insert({
        event_type: 'event_click',
        event_source: 'home_page',
        source_id: eventId,
        metadata: { event_id: eventId }
      });
    } catch (error) {
      console.error('Failed to track event click:', error);
    }
  };

  // Using fallback data imported from @/data/fallback-data

  // Use fallback data if no deals or events are returned from the database
  const displayDeals = deals.length > 0 ? deals : fallbackDeals;
  const displayEvents = events.length > 0 ? events : fallbackEvents;

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="container mx-auto px-4 py-8">
        {/* Environment warning will only show in development mode */}
        <EnvWarning />

        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-4">
            Welcome to <span className="text-indigo-600">CityPulse</span> South Africa
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Discover the best local deals and events across South Africa.
            Connect with your community and never miss out on amazing opportunities.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/deals">
              <Button size="lg" className="w-full sm:w-auto">
                <Tag className="mr-2 h-5 w-5" />
                Explore Deals
              </Button>
            </Link>
            <Link to="/events">
              <Button variant="outline" size="lg" className="w-full sm:w-auto">
                <Calendar className="mr-2 h-5 w-5" />
                Find Events
              </Button>
            </Link>
            <Link to="/ai-assistant">
              <Button variant="outline" size="lg" className="w-full sm:w-auto">
                🤖 AI Assistant
              </Button>
            </Link>
          </div>
        </div>

        {/* Featured Deals and Events Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
          <section>
            <h2 className="text-3xl font-bold mb-6 flex items-center gap-2">
              <Tag className="h-6 w-6 text-indigo-600" /> Featured Deals
            </h2>

            <div className="space-y-4">
              {loading ? (
                <LoadingState isLoading={true} type="card" count={3} />
              ) : (
                displayDeals.map(deal => (
                  <SimpleDealCard
                    key={deal.id}
                    deal={deal}
                    onClick={() => {
                      handleDealClick(deal.id);
                      window.location.href = `/deals/${deal.id}`;
                    }}
                  />
                ))
              )}
            </div>

            <div className="mt-6">
              <Link to="/deals">
                <Button variant="outline" size="lg" className="w-full">
                  View All Deals
                </Button>
              </Link>
            </div>
          </section>

          <section>
            <h2 className="text-3xl font-bold mb-6 flex items-center gap-2">
              <Calendar className="h-6 w-6 text-indigo-600" /> Upcoming Events
            </h2>

            <div className="space-y-4">
              {loading ? (
                <LoadingState isLoading={true} type="card" count={3} />
              ) : (
                displayEvents.map(event => (
                  <SimpleEventCard
                    key={event.id}
                    event={event}
                    onClick={() => {
                      handleEventClick(event.id);
                      window.location.href = `/events/${event.id}`;
                    }}
                  />
                ))
              )}
            </div>

            <div className="mt-6">
              <Link to="/events">
                <Button variant="outline" size="lg" className="w-full">
                  View All Events
                </Button>
              </Link>
            </div>
          </section>
        </div>

        {/* Call to Action Section */}
        <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl p-8 text-center text-white">
          <h3 className="text-2xl font-bold mb-4">Ready to explore South Africa?</h3>
          <p className="text-lg mb-6 opacity-90">
            Join thousands of South Africans discovering amazing local experiences every day.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/sign-up">
              <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                Get Started
              </Button>
            </Link>
            <Link to="/ai-assistant">
              <Button size="lg" variant="outline" className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-indigo-600">
                Try AI Assistant
              </Button>
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Index;
