import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Star, TrendingUp, Users, MapPin, Calendar, Megaphone, BarChart3, Sparkles } from 'lucide-react';

export const Pricing = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600">
            Why Advertise with CityPulse?
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Join hundreds of local businesses promoting their deals and events across South Africa.
            Reach thousands of customers actively looking for local experiences.
          </p>

          {/* Hero Image Mockup */}
          <div className="relative max-w-4xl mx-auto mb-12">
            <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl p-8 shadow-2xl">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                      <TrendingUp className="h-8 w-8 text-green-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">Increase Sales</h3>
                    <p className="text-sm text-gray-600">Average 40% boost in foot traffic</p>
                  </div>
                  <div className="text-center">
                    <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="h-8 w-8 text-blue-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">Reach Customers</h3>
                    <p className="text-sm text-gray-600">Connect with local community</p>
                  </div>
                  <div className="text-center">
                    <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                      <BarChart3 className="h-8 w-8 text-purple-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">Track Results</h3>
                    <p className="text-sm text-gray-600">Real-time analytics & insights</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Deal Packages Section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-gray-900">Deal Packages</h2>
            <p className="text-lg text-gray-600">Promote your special offers and discounts</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Standard Deal Package */}
            <Card className="relative overflow-hidden border-2 hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-blue-500 to-cyan-500"></div>
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-2xl font-bold text-gray-900">Standard Deal</CardTitle>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-700">Popular</Badge>
                </div>
                <CardDescription className="text-lg">Basic listing with standard visibility until expiry</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold text-gray-900">R99</span>
                    <span className="text-lg text-gray-600 ml-2">/week</span>
                  </div>
                </div>

                {/* Mockup Image */}
                <div className="bg-gradient-to-br from-blue-100 to-cyan-100 rounded-lg p-6 mb-6">
                  <div className="bg-white rounded-lg p-4 shadow-sm">
                    <div className="flex items-center mb-3">
                      <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                        <Megaphone className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Your Deal Here</h4>
                        <p className="text-sm text-gray-600">Standard listing visibility</p>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">Listed in category • Basic placement</div>
                  </div>
                </div>

                <ul className="space-y-3 mb-6">
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Standard visibility in search results</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Category-based listing</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Basic analytics dashboard</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Active until deal expiry</span>
                  </li>
                </ul>

                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  Choose Standard
                </Button>
              </CardContent>
            </Card>

            {/* Premium Deal Package */}
            <Card className="relative overflow-hidden border-2 border-purple-200 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-purple-500 to-pink-500"></div>
              <div className="absolute top-4 right-4">
                <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                  <Star className="h-3 w-3 mr-1" />
                  Recommended
                </Badge>
              </div>
              <CardHeader className="pb-4">
                <CardTitle className="text-2xl font-bold text-gray-900">Premium Deal</CardTitle>
                <CardDescription className="text-lg">Featured placement, enhanced visibility, and analytics until expiry</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold text-gray-900">R250</span>
                    <span className="text-lg text-gray-600 ml-2">/week</span>
                  </div>
                </div>

                {/* Mockup Image */}
                <div className="bg-gradient-to-br from-purple-100 to-pink-100 rounded-lg p-6 mb-6">
                  <div className="bg-white rounded-lg p-4 shadow-lg border-2 border-purple-200">
                    <div className="flex items-center mb-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                        <Star className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Your Featured Deal</h4>
                        <p className="text-sm text-purple-600 font-medium">⭐ FEATURED</p>
                      </div>
                    </div>
                    <div className="text-xs text-purple-600 font-medium">Top placement • Enhanced visibility</div>
                  </div>
                </div>

                <ul className="space-y-3 mb-6">
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span className="font-medium">Featured placement at top</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Enhanced visibility in search</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Advanced analytics & insights</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Priority customer support</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Social media promotion</span>
                  </li>
                </ul>

                <Button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                  Choose Premium
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Event Packages Section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-gray-900">Event Packages</h2>
            <p className="text-lg text-gray-600">Promote your events and gatherings</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Standard Event Package */}
            <Card className="relative overflow-hidden border-2 hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-green-500 to-emerald-500"></div>
              <CardHeader className="pb-4">
                <CardTitle className="text-2xl font-bold text-gray-900">Standard Event</CardTitle>
                <CardDescription className="text-lg">Basic listing with standard visibility until event date</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold text-gray-900">R299</span>
                    <span className="text-lg text-gray-600 ml-2">/event</span>
                  </div>
                </div>

                {/* Mockup Image */}
                <div className="bg-gradient-to-br from-green-100 to-emerald-100 rounded-lg p-6 mb-6">
                  <div className="bg-white rounded-lg p-4 shadow-sm">
                    <div className="flex items-center mb-3">
                      <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                        <Calendar className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Your Event Here</h4>
                        <p className="text-sm text-gray-600">Dec 25, 2024 • Cape Town</p>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">Events category • Standard placement</div>
                  </div>
                </div>

                <ul className="space-y-3 mb-6">
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Standard event listing</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Date and location display</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Basic event analytics</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Active until event date</span>
                  </li>
                </ul>

                <Button className="w-full bg-green-600 hover:bg-green-700">
                  Choose Standard Event
                </Button>
              </CardContent>
            </Card>

            {/* Premium Event Package */}
            <Card className="relative overflow-hidden border-2 border-orange-200 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-orange-500 to-red-500"></div>
              <div className="absolute top-4 right-4">
                <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white">
                  <Sparkles className="h-3 w-3 mr-1" />
                  Featured
                </Badge>
              </div>
              <CardHeader className="pb-4">
                <CardTitle className="text-2xl font-bold text-gray-900">Premium Event</CardTitle>
                <CardDescription className="text-lg">Featured placement, homepage highlight, and social media promotion until event date</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold text-gray-900">R460</span>
                    <span className="text-lg text-gray-600 ml-2">/event</span>
                  </div>
                </div>

                {/* Mockup Image */}
                <div className="bg-gradient-to-br from-orange-100 to-red-100 rounded-lg p-6 mb-6">
                  <div className="bg-white rounded-lg p-4 shadow-lg border-2 border-orange-200">
                    <div className="flex items-center mb-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center mr-3">
                        <Star className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Your Featured Event</h4>
                        <p className="text-sm text-orange-600 font-medium">🌟 HOMEPAGE FEATURED</p>
                      </div>
                    </div>
                    <div className="text-xs text-orange-600 font-medium">Homepage highlight • Social promotion</div>
                  </div>
                </div>

                <ul className="space-y-3 mb-6">
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span className="font-medium">Featured on homepage</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Enhanced event visibility</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Social media promotion</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Advanced event analytics</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span>Priority event support</span>
                  </li>
                </ul>

                <Button className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700">
                  Choose Premium Event
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* For Businesses Section */}
        <div className="bg-white rounded-2xl p-8 shadow-xl mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4 text-gray-900">For Businesses</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Increase foot traffic and sales by reaching thousands of local customers actively looking for deals and events in South Africa.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-indigo-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="h-10 w-10 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900">Local Reach</h3>
              <p className="text-gray-600">Connect with customers in your area who are actively searching for local businesses and experiences.</p>
            </div>
            <div className="text-center">
              <div className="bg-green-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-10 w-10 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900">Increase Sales</h3>
              <p className="text-gray-600">Drive more foot traffic and boost revenue with targeted promotions that reach the right audience.</p>
            </div>
            <div className="text-center">
              <div className="bg-purple-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="h-10 w-10 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900">Track Results</h3>
              <p className="text-gray-600">Monitor your campaign performance with detailed analytics and insights to optimize your marketing.</p>
            </div>
          </div>
        </div>

        {/* User Accounts Section */}
        <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl p-8 text-white text-center">
          <h2 className="text-3xl font-bold mb-4">User Accounts</h2>
          <p className="text-lg mb-6 text-indigo-100 max-w-3xl mx-auto">
            Create an account to save your favorite deals and events, receive personalized recommendations, and get notified about new offers in your area.
          </p>
          <Button size="lg" variant="secondary" className="bg-white text-indigo-600 hover:bg-gray-100">
            Create Free Account
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Pricing;