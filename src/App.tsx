import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { StripeProvider } from './contexts/StripeContext';
import { SimpleLayout } from './components/layout/SimpleLayout';
import { Index } from './pages/Index';
import { Deals } from './pages/Deals';
import { Events } from './pages/Events';
import { Pricing } from './pages/Pricing';
import { Contact } from './pages/Contact';
import { Terms } from './pages/Terms';
import { Privacy } from './pages/Privacy';
import { NotFound } from './pages/NotFound';
import { PulsePal } from './components/ai/PulsePal';
import './App.css';

// AI Assistant page
const AIAssistant = () => (
  <div className="p-6 max-w-4xl mx-auto">
    <h1 className="text-3xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 to-purple-600">
      CityPulse AI Assistant
    </h1>
    <div className="border rounded-lg bg-white shadow-md overflow-hidden">
      <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-400 p-4">
        <h2 className="text-white text-xl font-bold">PulsePal AI</h2>
        <p className="text-white text-opacity-90">
          Ask me anything about local deals, events, or get personalized recommendations
        </p>
      </div>
      <div className="p-6">
        <PulsePal apiKey={import.meta.env.VITE_GEMINI_API_KEY || ""} />
      </div>
    </div>
  </div>
);

function App() {
  console.log('App component rendering...');

  return (
    <BrowserRouter>
      <StripeProvider>
        <SimpleLayout>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/deals" element={<Deals />} />
            <Route path="/events" element={<Events />} />
            <Route path="/pricing" element={<Pricing />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/ai-assistant" element={<AIAssistant />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </SimpleLayout>
      </StripeProvider>
    </BrowserRouter>
  );
}

export default App;
