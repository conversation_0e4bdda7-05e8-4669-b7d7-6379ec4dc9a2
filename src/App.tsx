import { BrowserRouter, Routes, Route } from 'react-router-dom';
import './App.css';

// Simple Home component
const Home = () => (
  <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
    <div className="max-w-4xl mx-auto text-center p-8">
      <h1 className="text-6xl font-bold text-gray-900 mb-6">
        🌟 CityPulse SA
      </h1>
      <p className="text-xl text-gray-600 mb-8">
        Your Gateway to Local Deals, Events & Experiences in South Africa
      </p>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold text-gray-800 mb-3">🎯 Local Deals</h3>
          <p className="text-gray-600">Discover amazing deals from local businesses in your area</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold text-gray-800 mb-3">🎉 Events</h3>
          <p className="text-gray-600">Stay updated with the latest events and activities</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold text-gray-800 mb-3">💼 For Business</h3>
          <p className="text-gray-600">Grow your business with our merchant platform</p>
        </div>
      </div>
      <div className="mt-12">
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          ✅ <strong>App is Working!</strong> - Development server is running successfully
        </div>
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
          🚀 <strong>Ready for Deployment!</strong> - This app can be deployed to Vercel
        </div>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
          🔧 <strong>Next Steps:</strong> Configure authentication and add more features
        </div>
      </div>
    </div>
  </div>
);

// About page
const About = () => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center">
    <div className="max-w-2xl mx-auto text-center p-8">
      <h1 className="text-4xl font-bold text-gray-900 mb-6">About CityPulse SA</h1>
      <p className="text-lg text-gray-600 mb-4">
        CityPulse SA is a comprehensive platform connecting South African communities
        with local businesses, events, and opportunities.
      </p>
      <p className="text-lg text-gray-600">
        Built with React, TypeScript, and modern web technologies for the best user experience.
      </p>
    </div>
  </div>
);

// Contact page
const Contact = () => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center">
    <div className="max-w-2xl mx-auto text-center p-8">
      <h1 className="text-4xl font-bold text-gray-900 mb-6">Contact Us</h1>
      <p className="text-lg text-gray-600 mb-4">
        Get in touch with the CityPulse SA team
      </p>
      <div className="bg-white p-6 rounded-lg shadow-md">
        <p className="text-gray-600">📧 Email: <EMAIL></p>
        <p className="text-gray-600">📱 Phone: +27 (0) 123 456 789</p>
        <p className="text-gray-600">📍 Location: South Africa</p>
      </div>
    </div>
  </div>
);

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/about" element={<About />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="*" element={
          <div className="min-h-screen bg-gray-50 flex items-center justify-center">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">404 - Page Not Found</h1>
              <p className="text-gray-600">The page you're looking for doesn't exist.</p>
              <a href="/" className="text-blue-600 hover:text-blue-800 mt-4 inline-block">
                ← Back to Home
              </a>
            </div>
          </div>
        } />
      </Routes>
    </BrowserRouter>
  );
}

export default App;
