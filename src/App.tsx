import { BrowserRouter, Routes, Route } from 'react-router-dom';

// Simple test pages
const HomePage = () => (
  <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div className="container mx-auto px-4 py-16">
      <div className="text-center">
        <h1 className="text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600">
          🏠 CityPulse SA Homepage
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
          Welcome to your local business discovery platform
        </p>
        <div className="bg-white rounded-2xl p-8 shadow-xl max-w-2xl mx-auto">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">✅ Homepage Working</h2>
          <p className="text-gray-600">Router and navigation are functional!</p>
        </div>
      </div>
    </div>
  </div>
);

const DealsPage = () => (
  <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
    <div className="container mx-auto px-4 py-16">
      <div className="text-center">
        <h1 className="text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-teal-600">
          💰 Deals Page
        </h1>
        <div className="bg-white rounded-2xl p-8 shadow-xl max-w-2xl mx-auto">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">✅ Deals Page Working</h2>
          <p className="text-gray-600">This will show amazing local deals!</p>
        </div>
      </div>
    </div>
  </div>
);

const EventsPage = () => (
  <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50">
    <div className="container mx-auto px-4 py-16">
      <div className="text-center">
        <h1 className="text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-pink-600">
          🎉 Events Page
        </h1>
        <div className="bg-white rounded-2xl p-8 shadow-xl max-w-2xl mx-auto">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">✅ Events Page Working</h2>
          <p className="text-gray-600">This will show exciting local events!</p>
        </div>
      </div>
    </div>
  </div>
);

function App() {
  console.log('App component rendering...');

  return (
    <BrowserRouter>
      <nav className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-8">
              <h1 className="text-xl font-bold text-indigo-600">CityPulse SA</h1>
              <div className="flex space-x-4">
                <a href="/" className="text-gray-600 hover:text-indigo-600">Home</a>
                <a href="/deals" className="text-gray-600 hover:text-indigo-600">Deals</a>
                <a href="/events" className="text-gray-600 hover:text-indigo-600">Events</a>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/deals" element={<DealsPage />} />
        <Route path="/events" element={<EventsPage />} />
        <Route path="*" element={
          <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center">
            <div className="text-center">
              <h1 className="text-4xl font-bold mb-4 text-gray-900">404 - Page Not Found</h1>
              <p className="text-gray-600">The page you're looking for doesn't exist.</p>
            </div>
          </div>
        } />
      </Routes>
    </BrowserRouter>
  );
}

export default App;
