import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/clerk-react';
import { StripeProvider } from './contexts/StripeContext';
import { SimpleLayout } from './components/layout/SimpleLayout';
import { Index } from './pages/Index';
import { Deals } from './pages/Deals';
import { Events } from './pages/Events';
import { Contact } from './pages/Contact';
import { Pricing } from './pages/Pricing';
import { Terms } from './pages/Terms';
import { Privacy } from './pages/Privacy';
import { NotFound } from './pages/NotFound';
import { CheckoutSuccess } from './pages/CheckoutSuccess';
import SignIn from './pages/SignIn';
import SignUp from './pages/SignUp';
import Profile from './pages/Profile';
import { PulsePal } from './components/ai/PulsePal';
import './App.css';

// Get the publishable key from environment variables
const CLERK_PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

// AI Assistant page
const AIAssistant = () => (
  <div className="p-6 max-w-4xl mx-auto">
    <h1 className="text-3xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 to-purple-600">
      CityPulse AI Assistant
    </h1>
    <div className="border rounded-lg bg-white shadow-md overflow-hidden">
      <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-400 p-4">
        <h2 className="text-white text-xl font-bold">PulsePal AI</h2>
        <p className="text-white text-opacity-90">
          Ask me anything about local deals, events, or get personalized recommendations
        </p>
      </div>
      <div className="p-6">
        <PulsePal apiKey={import.meta.env.VITE_GEMINI_API_KEY || ""} />
      </div>
    </div>
  </div>
);

// Simple demo component for when Clerk is not configured
const DemoApp = () => (
  <BrowserRouter>
    <StripeProvider>
      <SimpleLayout>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/deals" element={<Deals />} />
          <Route path="/events" element={<Events />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/pricing" element={<Pricing />} />
          <Route path="/terms" element={<Terms />} />
          <Route path="/privacy" element={<Privacy />} />
          <Route path="/ai-assistant" element={<AIAssistant />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </SimpleLayout>
    </StripeProvider>
  </BrowserRouter>
);

// Full app with Clerk authentication
const AuthenticatedApp = () => (
  <BrowserRouter>
    <ClerkProvider publishableKey={CLERK_PUBLISHABLE_KEY}>
      <StripeProvider>
        <SimpleLayout>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/sign-in" element={<SignIn />} />
            <Route path="/sign-up" element={<SignUp />} />
            <Route path="/deals" element={<Deals />} />
            <Route path="/events" element={<Events />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/pricing" element={<Pricing />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/checkout-success" element={<CheckoutSuccess />} />
            <Route path="/ai-assistant" element={<AIAssistant />} />
            <Route path="/merchant/dashboard" element={<div className="p-8 text-center"><h1 className="text-2xl font-bold">Merchant Dashboard</h1><p>Coming Soon - Under Development</p></div>} />
            <Route path="/admin/dashboard" element={<div className="p-8 text-center"><h1 className="text-2xl font-bold">Admin Dashboard</h1><p>Coming Soon - Under Development</p></div>} />
            <Route path="/profile" element={<Profile />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </SimpleLayout>
      </StripeProvider>
    </ClerkProvider>
  </BrowserRouter>
);

function App() {
  // Check if Clerk is configured
  if (!CLERK_PUBLISHABLE_KEY) {
    console.warn('Clerk publishable key not found. Running in demo mode.');
    return <DemoApp />;
  }

  return <AuthenticatedApp />;
}

export default App;
