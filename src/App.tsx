import { useState } from "react";
import './App.css';

export default function App() {
  const [count, setCount] = useState(0);

  return (
    <div className="App">
      <header className="App-header">
        <h1>CityPulse South Africa</h1>
        <p>Welcome to your updated CityPulse application!</p>

        <div className="card">
          <button onClick={() => setCount((count) => count + 1)}>
            count is {count}
          </button>
          <p>
            Edit <code>src/App.tsx</code> and save to test HMR
          </p>
        </div>

        <p className="read-the-docs">
          Your files have been successfully updated from your laptop!
        </p>
      </header>
    </div>
  );
}
