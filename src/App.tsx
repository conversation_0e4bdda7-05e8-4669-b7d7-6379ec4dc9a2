import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { Clerk<PERSON>rovider } from '@clerk/clerk-react';
import { StripeProvider } from './contexts/StripeContext';
import { Layout } from './components/layout/Layout';
import { Index } from './pages/Index';
import { Deals } from './pages/Deals';
import { Events } from './pages/Events';
import { Contact } from './pages/Contact';
import { Pricing } from './pages/Pricing';
import { Terms } from './pages/Terms';
import { Privacy } from './pages/Privacy';
import { NotFound } from './pages/NotFound';
import { CheckoutSuccess } from './pages/CheckoutSuccess';
// import MerchantDashboard from './pages/merchant/MerchantDashboard';
// import AdminDashboard from './pages/admin/AdminDashboard';
import SignIn from './pages/SignIn';
import SignUp from './pages/SignUp';
import Profile from './pages/Profile';
import './App.css';

// Get the publishable key from environment variables
const CLERK_PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;



// Simple demo component for when Clerk is not configured
const DemoApp = () => (
  <BrowserRouter>
    <StripeProvider>
      <Layout>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/deals" element={<Deals />} />
          <Route path="/events" element={<Events />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/pricing" element={<Pricing />} />
          <Route path="/terms" element={<Terms />} />
          <Route path="/privacy" element={<Privacy />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Layout>
    </StripeProvider>
  </BrowserRouter>
);

// Full app with Clerk authentication
const AuthenticatedApp = () => (
  <BrowserRouter>
    <ClerkProvider publishableKey={CLERK_PUBLISHABLE_KEY}>
      <StripeProvider>
        <Layout>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/sign-in" element={<SignIn />} />
            <Route path="/sign-up" element={<SignUp />} />
            <Route path="/deals" element={<Deals />} />
            <Route path="/events" element={<Events />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/pricing" element={<Pricing />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/checkout-success" element={<CheckoutSuccess />} />
            <Route path="/merchant/dashboard" element={<div className="p-8 text-center"><h1 className="text-2xl font-bold">Merchant Dashboard</h1><p>Coming Soon - Under Development</p></div>} />
            <Route path="/admin/dashboard" element={<div className="p-8 text-center"><h1 className="text-2xl font-bold">Admin Dashboard</h1><p>Coming Soon - Under Development</p></div>} />
            <Route path="/profile" element={<Profile />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </Layout>
      </StripeProvider>
    </ClerkProvider>
  </BrowserRouter>
);

function App() {
  // Check if Clerk is configured
  if (!CLERK_PUBLISHABLE_KEY) {
    console.warn('Clerk publishable key not found. Running in demo mode.');
    return <DemoApp />;
  }

  return <AuthenticatedApp />;
}

export default App;
