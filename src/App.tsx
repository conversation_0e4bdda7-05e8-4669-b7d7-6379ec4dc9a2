import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { SimpleLayout } from './components/layout/SimpleLayout';
import { Index } from './pages/Index';
import { Deals } from './pages/Deals';
import { Events } from './pages/Events';
import { Pricing } from './pages/Pricing';
import { PulsePal } from './components/ai/PulsePal';
import './App.css';

// AI Assistant page
const AIAssistant = () => (
  <div className="p-6 max-w-4xl mx-auto">
    <h1 className="text-3xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 to-purple-600">
      CityPulse AI Assistant
    </h1>
    <div className="border rounded-lg bg-white shadow-md overflow-hidden">
      <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-400 p-4">
        <h2 className="text-white text-xl font-bold">PulsePal AI</h2>
        <p className="text-white text-opacity-90">
          Ask me anything about local deals, events, or get personalized recommendations
        </p>
      </div>
      <div className="p-6">
        <PulsePal apiKey={import.meta.env.VITE_GEMINI_API_KEY || ""} />
      </div>
    </div>
  </div>
);

function App() {
  return (
    <BrowserRouter>
      <SimpleLayout>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/deals" element={<Deals />} />
          <Route path="/events" element={<Events />} />
          <Route path="/pricing" element={<Pricing />} />
          <Route path="/ai-assistant" element={<AIAssistant />} />
          <Route path="*" element={
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
              <div className="text-center">
                <h1 className="text-4xl font-bold mb-4 text-gray-900">404 - Page Not Found</h1>
                <p className="text-gray-600">The page you're looking for doesn't exist.</p>
              </div>
            </div>
          } />
        </Routes>
      </SimpleLayout>
    </BrowserRouter>
  );
}

export default App;
