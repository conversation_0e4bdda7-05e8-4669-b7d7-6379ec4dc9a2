import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import { resolve } from "path";
import { VitePWA } from 'vite-plugin-pwa'

// Only use lovable-tagger in development, and only if available.
// This avoids ESM/CJS issues and keeps prod builds clean and cheap.
async function getDevPlugins(mode) {
  const plugins = [react()];
  if (mode === 'development') {
    try {
      const { componentTagger } = await import('lovable-tagger');
      plugins.push(componentTagger());
    } catch (e) {
      console.warn('lovable-tagger not found or failed to load:', e);
    }
  }
  plugins.push(VitePWA({
    registerType: 'autoUpdate',
    includeAssets: [
      'favicon.svg',
      'icons/*.png',
      'icons/*.svg'
    ],
    manifest: {
      name: 'CityPulse',
      short_name: 'CityPulse',
      description: 'Your Local Business Discovery Platform',
      theme_color: '#0EA5E9',
      background_color: '#ffffff',
      display: 'standalone',
      orientation: 'portrait',
      icons: [
        {
          src: 'icons/icon-72x72.png',
          sizes: '72x72',
          type: 'image/png'
        },
        {
          src: 'icons/icon-96x96.png',
          sizes: '96x96',
          type: 'image/png'
        },
        {
          src: 'icons/icon-128x128.png',
          sizes: '128x128',
          type: 'image/png'
        },
        {
          src: 'icons/icon-144x144.png',
          sizes: '144x144',
          type: 'image/png'
        },
        {
          src: 'icons/icon-152x152.png',
          sizes: '152x152',
          type: 'image/png'
        },
        {
          src: 'icons/icon-192x192.png',
          sizes: '192x192',
          type: 'image/png'
        },
        {
          src: 'icons/icon-384x384.png',
          sizes: '384x384',
          type: 'image/png'
        },
        {
          src: 'icons/icon-512x512.png',
          sizes: '512x512',
          type: 'image/png'
        },
        {
          src: 'icons/maskable-icon-192x192.png',
          sizes: '192x192',
          type: 'image/png',
          purpose: 'maskable'
        },
        {
          src: 'icons/maskable-icon-512x512.png',
          sizes: '512x512',
          type: 'image/png',
          purpose: 'maskable'
        }
      ]
    }
  }));
  return plugins.filter(Boolean);
}

// https://vitejs.dev/config/
export default defineConfig(async ({ mode }) => {
  const plugins = await getDevPlugins(mode);
  
  return {
    plugins,
    resolve: {
      alias: {
        '@': resolve(__dirname, './src')
      }
    },
    build: {
      sourcemap: true,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom', 'react-router-dom'],
            ui: ['@clerk/clerk-react', '@stripe/stripe-js']
          }
        }
      }
    },
    server: {
      port: 3000,
      strictPort: true,
      host: true
    }
  };
});
