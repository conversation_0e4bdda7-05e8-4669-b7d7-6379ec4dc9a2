{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/assets/(.*)", "headers": {"cache-control": "public, max-age=31536000, immutable"}, "continue": true}, {"src": "/(.*\\.(js|css|svg|ico|jpg|jpeg|png|webp|avif|gif|woff|woff2)$)", "headers": {"cache-control": "public, max-age=31536000, immutable"}, "continue": true}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"VITE_SUPABASE_URL": "https://qghojdkspxhyjeurxagx.supabase.co", "VITE_APP_NAME": "CityPulse South Africa", "VITE_APP_ENV": "production"}, "build": {"env": {"VITE_SUPABASE_URL": "https://qghojdkspxhyjeurxagx.supabase.co", "VITE_APP_NAME": "CityPulse South Africa", "VITE_APP_ENV": "production"}}}