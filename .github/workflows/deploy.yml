name: Deploy to Vercel

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
  workflow_dispatch:

# Define environment variables at the workflow level (non-sensitive only)
env:
  # App configuration (non-sensitive)
  VITE_APP_NAME: "CityPulse South Africa"
  VITE_APP_ENV: "production"
  VITE_APP_URL: "https://citypulse.co.za"

  # Default Supabase URL (public, not a secret)
  DEFAULT_SUPABASE_URL: "https://qghojdkspxhyjeurxagx.supabase.co"

  # Feature flags (enabled in production)
  VITE_ENABLE_GEOLOCATION: "true"
  VITE_ENABLE_PAYMENTS: "true"
  VITE_ENABLE_SOCIAL_SHARING: "true"
  VITE_ENABLE_NOTIFICATIONS: "true"

  # Default map center (Johannesburg)
  VITE_DEFAULT_LATITUDE: "-26.2041"
  VITE_DEFAULT_LONGITUDE: "28.0473"
  VITE_DEFAULT_ZOOM: "12"

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test

      - name: Run type check
        run: npm run type-check

      - name: Run linting
        run: npm run lint

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      # Check for secrets without directly referencing them
      - name: Check for secrets
        id: check_secrets
        run: |
          # This step checks if secrets exist without accessing their values directly

          # Initialize result variables
          HAS_SUPABASE_SECRETS="false"
          HAS_VERCEL_SECRETS="false"

          # Check if environment variables are set (they will be empty strings if secrets don't exist)
          if [ -n "$CHECK_SUPABASE_URL" ] && [ -n "$CHECK_SUPABASE_ANON_KEY" ]; then
            HAS_SUPABASE_SECRETS="true"
            echo "Supabase secrets found"
          else
            echo "::warning::Missing Supabase secrets. Will use default configuration."
          fi

          if [ -n "$CHECK_VERCEL_TOKEN" ] && [ -n "$CHECK_VERCEL_ORG_ID" ] && [ -n "$CHECK_VERCEL_PROJECT_ID" ]; then
            HAS_VERCEL_SECRETS="true"
            echo "Vercel secrets found"
          else
            echo "::warning::Missing Vercel deployment secrets."
          fi

          # Export variables for later steps
          echo "has_supabase_secrets=${HAS_SUPABASE_SECRETS}" >> $GITHUB_OUTPUT
          echo "has_vercel_secrets=${HAS_VERCEL_SECRETS}" >> $GITHUB_OUTPUT
        env:
          # We're just checking if these are set, not accessing their values
          CHECK_SUPABASE_URL: ${{ secrets.SUPABASE_URL != '' && '1' || '' }}
          CHECK_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY != '' && '1' || '' }}
          CHECK_VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN != '' && '1' || '' }}
          CHECK_VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID != '' && '1' || '' }}
          CHECK_VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID != '' && '1' || '' }}

      # Set up Supabase environment variables if secrets are available
      - name: Configure Supabase with secrets
        id: supabase_config
        if: steps.check_secrets.outputs.has_supabase_secrets == 'true'
        run: |
          # Store Supabase URL and key for the build step
          echo "supabase_url=$SUPABASE_URL" >> $GITHUB_OUTPUT
          echo "supabase_anon_key=$SUPABASE_ANON_KEY" >> $GITHUB_OUTPUT
          echo "Using configured Supabase project"
        env:
          # These are used only in this step
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}

      # Set up default Supabase URL if secrets are not available
      - name: Configure default Supabase
        id: supabase_default
        if: steps.check_secrets.outputs.has_supabase_secrets != 'true'
        run: |
          # Use default Supabase URL and empty key
          echo "supabase_url=${DEFAULT_SUPABASE_URL}" >> $GITHUB_OUTPUT
          echo "supabase_anon_key=" >> $GITHUB_OUTPUT
          echo "Using default Supabase configuration"

      # Set up Vercel credentials if available
      - name: Configure Vercel
        id: vercel_config
        if: steps.check_secrets.outputs.has_vercel_secrets == 'true'
        run: |
          # Store Vercel credentials as environment variables for the deployment step
          echo "vercel_token=$VERCEL_TOKEN" >> $GITHUB_OUTPUT
          echo "vercel_org_id=$VERCEL_ORG_ID" >> $GITHUB_OUTPUT
          echo "vercel_project_id=$VERCEL_PROJECT_ID" >> $GITHUB_OUTPUT
          echo "Vercel credentials configured for deployment"
        env:
          # These are used only in this step
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

      # Build the application with the appropriate environment variables
      - name: Build
        run: npm run build
        env:
          # Supabase configuration
          VITE_SUPABASE_URL: ${{ steps.supabase_config.outputs.supabase_url || steps.supabase_default.outputs.supabase_url }}
          VITE_SUPABASE_ANON_KEY: ${{ steps.supabase_config.outputs.supabase_anon_key || steps.supabase_default.outputs.supabase_anon_key }}
          NEXT_PUBLIC_SUPABASE_URL: ${{ steps.supabase_config.outputs.supabase_url || steps.supabase_default.outputs.supabase_url }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ steps.supabase_config.outputs.supabase_anon_key || steps.supabase_default.outputs.supabase_anon_key }}

          # Stripe configuration
          VITE_STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY || 'pk_live_51IRNxfHieGkyNl5wOGvmEAtaXxZ6VHEPmHcXuwfsfOPTt0umFFEY9QpsJMXo4IAo0uzl0R66CpaJFRKCaXo0k5DZ00uGSXCeCN' }}

          # Email configuration
          VITE_SENDGRID_FROM_EMAIL: "<EMAIL>"
          VITE_SENDGRID_FROM_NAME: "CityPulse South Africa"

          # Feature flags are inherited from the workflow level

      # Deploy to Vercel only if all required secrets are available
      - name: Deploy to Vercel
        if: steps.check_secrets.outputs.has_vercel_secrets == 'true'
        uses: amondnet/vercel-action@v25
        with:
          # Use outputs from the vercel_config step
          vercel-token: ${{ steps.vercel_config.outputs.vercel_token }}
          vercel-org-id: ${{ steps.vercel_config.outputs.vercel_org_id }}
          vercel-project-id: ${{ steps.vercel_config.outputs.vercel_project_id }}
          working-directory: ./
          vercel-args: '--prod'

      # Provide instructions if deployment is skipped
      - name: Deployment skipped
        if: steps.check_secrets.outputs.has_vercel_secrets != 'true'
        run: |
          echo "Vercel deployment was skipped due to missing secrets."
          echo "To deploy to Vercel, please add the following secrets to your repository:"
          echo "- VERCEL_TOKEN: Your Vercel API token"
          echo "- VERCEL_ORG_ID: Your Vercel organization ID"
          echo "- VERCEL_PROJECT_ID: Your Vercel project ID"
          echo "You can find these values in your Vercel account settings."

      # Create a comprehensive workflow summary
      - name: Workflow Summary
        run: |
          echo "## CityPulse Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Build Status" >> $GITHUB_STEP_SUMMARY
          echo "✅ Application built successfully" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # Supabase configuration status
          echo "### Supabase Configuration" >> $GITHUB_STEP_SUMMARY
          if [ "${{ steps.check_secrets.outputs.has_supabase_secrets }}" == "true" ]; then
            echo "✅ **Complete** - Using configured Supabase project" >> $GITHUB_STEP_SUMMARY
            echo "- Project URL: \`${{ steps.supabase_config.outputs.supabase_url }}\`" >> $GITHUB_STEP_SUMMARY
          else
            echo "⚠️ **Incomplete** - Using default configuration" >> $GITHUB_STEP_SUMMARY
            echo "- Project URL: \`${DEFAULT_SUPABASE_URL}\`" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "To configure Supabase, add these secrets to your repository:" >> $GITHUB_STEP_SUMMARY
            echo "- \`SUPABASE_URL\`: Your Supabase project URL" >> $GITHUB_STEP_SUMMARY
            echo "- \`SUPABASE_ANON_KEY\`: Your Supabase anonymous key" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "You can find these values in your Supabase project settings." >> $GITHUB_STEP_SUMMARY
          fi

          echo "" >> $GITHUB_STEP_SUMMARY

          # Vercel deployment status
          echo "### Vercel Deployment" >> $GITHUB_STEP_SUMMARY
          if [ "${{ steps.check_secrets.outputs.has_vercel_secrets }}" == "true" ]; then
            echo "✅ **Complete** - Deployed to Vercel production environment" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ **Skipped** - Missing required secrets" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "To enable Vercel deployment, add these secrets to your repository:" >> $GITHUB_STEP_SUMMARY
            echo "- \`VERCEL_TOKEN\`: Your Vercel API token" >> $GITHUB_STEP_SUMMARY
            echo "- \`VERCEL_ORG_ID\`: Your Vercel organization ID" >> $GITHUB_STEP_SUMMARY
            echo "- \`VERCEL_PROJECT_ID\`: Your Vercel project ID" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "You can find these values in your Vercel account settings:" >> $GITHUB_STEP_SUMMARY
            echo "1. Go to https://vercel.com/account/tokens to create an API token" >> $GITHUB_STEP_SUMMARY
            echo "2. Find your Organization ID in the URL when viewing your team settings" >> $GITHUB_STEP_SUMMARY
            echo "3. Find your Project ID in the project settings under 'General'" >> $GITHUB_STEP_SUMMARY
          fi

          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Next Steps" >> $GITHUB_STEP_SUMMARY
          echo "- Visit your deployed application (if deployment succeeded)" >> $GITHUB_STEP_SUMMARY
          echo "- Set up any missing secrets for complete functionality" >> $GITHUB_STEP_SUMMARY
          echo "- Check the application logs if you encounter any issues" >> $GITHUB_STEP_SUMMARY
