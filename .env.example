# CityPulse Environment Configuration
# IMPORTANT: This file contains ACTUAL WORKING VALUES for the CityPulse application
# These values are required for full functionality

# Application Configuration
VITE_APP_NAME="CityPulse South Africa"
VITE_APP_ENV="development" # development, staging, production
VITE_APP_URL="http://localhost:5173"

# Supabase Configuration
# These are the actual working values for the CityPulse Supabase project
VITE_SUPABASE_URL="https://qghojdkspxhyjeurxagx.supabase.co"
VITE_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFnaG9qZGtzcHhoeWpldXJ4YWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ2NTU4NjUsImV4cCI6MjA2MDIzMTg2NX0.QInil2Wr7x14JwpRKKkIcgG6WwyOIUFx-O_kL8o2jdg"
# For Vercel compatibility
NEXT_PUBLIC_SUPABASE_URL="https://qghojdkspxhyjeurxagx.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFnaG9qZGtzcHhoeWpldXJ4YWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ2NTU4NjUsImV4cCI6MjA2MDIzMTg2NX0.QInil2Wr7x14JwpRKKkIcgG6WwyOIUFx-O_kL8o2jdg"
# For server-side operations (Edge Functions)
SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFnaG9qZGtzcHhoeWpldXJ4YWd4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTY5ODc2NDgwMCwiZXhwIjoyMDE0MzQwODAwfQ.service-role-key-placeholder"

# Stripe Configuration
# This is the actual working Stripe publishable key for CityPulse
VITE_STRIPE_PUBLISHABLE_KEY="pk_live_51IRNxfHieGkyNl5wOGvmEAtaXxZ6VHEPmHcXuwfsfOPTt0umFFEY9QpsJMXo4IAo0uzl0R66CpaJFRKCaXo0k5DZ00uGSXCeCN"
# These should be set in the GitHub repository secrets for production
STRIPE_SECRET_KEY="sk_live_your-stripe-secret-key"
STRIPE_WEBHOOK_SECRET="whsec_your-stripe-webhook-secret"

# Email Configuration (MailerSend)
# These are the actual working SMTP credentials for CityPulse
SMTP_HOST="smtp.mailersend.net"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="mssp.B2oFBll.7dnvo4dzpwnl5r86.ku3p4TO"
SMTP_ADMIN_EMAIL="<EMAIL>"
SMTP_SENDER_NAME="CityPulse South Africa"

# Email Configuration (SendGrid - if used)
VITE_SENDGRID_API_KEY="SG.your-sendgrid-api-key"
VITE_SENDGRID_FROM_EMAIL="<EMAIL>"
VITE_SENDGRID_FROM_NAME="CityPulse South Africa"

# Twilio Configuration (for SMS authentication)
TWILIO_ACCOUNT_SID="AC-your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="+27-your-twilio-phone-number"
TWILIO_MESSAGE_SERVICE_SID="MG-your-twilio-message-service-sid"

# OAuth Configuration
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
FACEBOOK_CLIENT_ID="your-facebook-client-id"
FACEBOOK_CLIENT_SECRET="your-facebook-client-secret"

# Geolocation Configuration
VITE_ENABLE_GEOLOCATION="true"
VITE_DEFAULT_LATITUDE="-26.2041"
VITE_DEFAULT_LONGITUDE="28.0473"
VITE_DEFAULT_ZOOM="12"
VITE_MAPBOX_ACCESS_TOKEN="pk.your-mapbox-access-token"

# Vercel Deployment (for CI/CD)
# These should be set in the GitHub repository secrets
VERCEL_TOKEN="your-vercel-token"
VERCEL_ORG_ID="your-vercel-org-id"
VERCEL_PROJECT_ID="your-vercel-project-id"
