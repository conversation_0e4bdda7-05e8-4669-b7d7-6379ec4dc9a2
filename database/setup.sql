-- CityPulse SA Database Setup
-- This script creates all the necessary tables for the CityPulse application

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Create deals table
CREATE TABLE IF NOT EXISTS deals (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    merchant_name VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    discount VARCHAR(50),
    expiration_date DATE,
    image_url TEXT,
    featured BOOLEAN DEFAULT FALSE,
    views INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create events table
CREATE TABLE IF NOT EXISTS events (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    merchant_name VARCHAR(255),
    category VARCHAR(100),
    date DATE NOT NULL,
    time TIME,
    location VARCHAR(255) NOT NULL,
    price VARCHAR(50),
    image_url TEXT,
    featured <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
    views INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create contact_submissions table
CREATE TABLE IF NOT EXISTS contact_submissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    message TEXT NOT NULL,
    subject VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create analytics table
CREATE TABLE IF NOT EXISTS analytics (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    event_source VARCHAR(100) NOT NULL,
    source_id INTEGER NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert sample deals data
INSERT INTO deals (title, description, merchant_name, category, discount, expiration_date, featured) VALUES
('50% Off Gourmet Burgers', 'Enjoy our premium beef burgers with artisanal toppings at half price!', 'The Burger Joint', 'Food & Drink', '50% OFF', '2024-12-31', true),
('Buy 2 Get 1 Free Coffee', 'Perfect morning deal for coffee lovers. Premium roasted beans.', 'Cape Town Coffee Co', 'Food & Drink', 'Buy 2 Get 1', '2024-12-25', false),
('30% Off Designer Clothing', 'Latest fashion trends at unbeatable prices. Limited time offer.', 'Fashion Forward', 'Retail', '30% OFF', '2024-12-20', true),
('Free Spa Treatment', 'Complimentary 60-minute massage with any facial treatment.', 'Serenity Spa', 'Beauty', 'Free Treatment', '2024-12-28', false),
('Movie Night Special', 'Two tickets for the price of one every Tuesday night.', 'Cinema City', 'Entertainment', '2 for 1', '2024-12-31', true);

-- Insert sample events data
INSERT INTO events (title, description, merchant_name, category, date, time, location, price, featured) VALUES
('Summer Music Festival', 'Three days of amazing live music featuring local and international artists.', 'Event Organizers SA', 'Music', '2024-12-15', '18:00', 'Cape Town Stadium', 'R350', true),
('Food & Wine Tasting', 'Experience the best of South African cuisine paired with premium wines.', 'Gourmet Events', 'Food & Shopping', '2024-12-18', '19:00', 'V&A Waterfront', 'R450', false),
('Business Networking Evening', 'Connect with entrepreneurs and business leaders in Cape Town.', 'Business Network SA', 'Networking', '2024-12-20', '17:30', 'Century City Conference Centre', 'R150', true),
('Rugby Championship', 'Local teams compete in this exciting rugby tournament.', 'Sports SA', 'Sports', '2024-12-22', '15:00', 'Newlands Rugby Ground', 'R80', false),
('Art Gallery Opening', 'Discover contemporary South African art at this exclusive opening.', 'Modern Art Gallery', 'Arts & Culture', '2024-12-25', '18:30', 'Zeitz Museum', 'Free', true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_deals_category ON deals(category);
CREATE INDEX IF NOT EXISTS idx_deals_featured ON deals(featured);
CREATE INDEX IF NOT EXISTS idx_deals_expiration ON deals(expiration_date);
CREATE INDEX IF NOT EXISTS idx_events_date ON events(date);
CREATE INDEX IF NOT EXISTS idx_events_category ON events(category);
CREATE INDEX IF NOT EXISTS idx_events_featured ON events(featured);
CREATE INDEX IF NOT EXISTS idx_analytics_event_type ON analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_created_at ON analytics(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE deals ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access
CREATE POLICY "Public read access for deals" ON deals FOR SELECT USING (true);
CREATE POLICY "Public read access for events" ON events FOR SELECT USING (true);

-- Create policies for analytics (allow inserts for tracking)
CREATE POLICY "Public insert access for analytics" ON analytics FOR INSERT WITH CHECK (true);

-- Create policies for contact submissions (allow inserts)
CREATE POLICY "Public insert access for contact_submissions" ON contact_submissions FOR INSERT WITH CHECK (true);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_deals_updated_at BEFORE UPDATE ON deals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_events_updated_at BEFORE UPDATE ON events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
