<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CityPulse UI Mockup</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sa-blue {
            background-color: #0032A0;
        }
        .sa-green {
            background-color: #007A4D;
        }
        .sidebar {
            width: 18rem;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 50;
        }
        .main-content {
            margin-left: 18rem;
        }
        .search-dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100;
        }
        .search-dialog-content {
            width: 600px;
            max-width: 90%;
            max-height: 90vh;
            background-color: white;
            border-radius: 0.5rem;
            overflow-y: auto;
        }
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar sa-blue text-white">
        <div class="h-full flex flex-col">
            <div class="flex items-center justify-between h-16 px-4 border-b border-blue-800">
                <a href="#" class="text-xl font-bold flex items-center gap-2">
                    <div class="bg-gradient-to-r from-white to-gray-200 p-1.5 rounded-md">
                        <span class="text-blue-900 font-bold">CP</span>
                    </div>
                    <span>CityPulse</span>
                </a>
                <button class="text-white md:hidden">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>

            <nav class="flex-1 pt-6 pb-4 overflow-y-auto">
                <!-- Main Navigation -->
                <div class="px-3 mb-6">
                    <h3 class="text-xs font-semibold text-white/70 uppercase tracking-wider px-3 mb-2">
                        Main Navigation
                    </h3>
                    <ul class="space-y-1">
                        <li>
                            <a href="#" class="flex items-center px-4 py-3 text-sm rounded-md bg-blue-700/70 font-medium">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                </svg>
                                <span>Home</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center px-4 py-3 text-sm rounded-md hover:bg-blue-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                </svg>
                                <span>Deals</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center px-4 py-3 text-sm rounded-md hover:bg-blue-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                                </svg>
                                <span>Events</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Merchant Section -->
                <div class="px-3 mb-6">
                    <h3 class="text-xs font-semibold text-white/70 uppercase tracking-wider px-3 mb-2">
                        Merchant
                    </h3>
                    <ul class="space-y-1">
                        <li>
                            <a href="#" class="flex items-center px-4 py-3 text-sm rounded-md hover:bg-blue-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                                <span>Merchant Login</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center px-4 py-3 text-sm rounded-md hover:bg-blue-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                                    <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd" />
                                </svg>
                                <span>Merchant Packages</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- More sections... -->
            </nav>

            <div class="p-4 border-t border-blue-800">
                <div class="px-4 py-2">
                    <div class="flex items-center">
                        <div class="h-10 w-10 rounded-full bg-blue-700 flex items-center justify-center">
                            <span class="font-medium text-white">CP</span>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium">CityPulse</p>
                            <p class="text-xs opacity-70">South Africa</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content p-4">
        <!-- Navbar -->
        <nav class="bg-white border-b border-gray-200 h-16 flex items-center justify-between px-4 md:px-6 mb-6">
            <div class="flex items-center space-x-4">
                <button class="md:hidden">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                    </svg>
                </button>

                <a href="#" class="flex items-center gap-2">
                    <div class="bg-gradient-to-r from-blue-600 to-green-600 p-1.5 rounded-md">
                        <span class="text-white font-bold">CP</span>
                    </div>
                    <span class="hidden sm:inline-block font-bold text-lg text-gray-800">CityPulse</span>
                </a>
            </div>

            <div class="hidden md:flex items-center flex-1 max-w-md mx-4">
                <button class="w-full flex justify-start pl-3 bg-gray-50 text-gray-500 hover:bg-gray-100 h-10 px-4 py-2 rounded-md border border-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                    </svg>
                    Search deals, events or locations...
                </button>
            </div>

            <div class="flex items-center space-x-3">
                <button class="text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 md:hidden" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                    </svg>
                </button>

                <button class="flex items-center gap-2 bg-blue-600 text-white px-3 py-2 rounded-md text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span>Login</span>
                </button>
            </div>
        </nav>

        <!-- Page Content -->
        <div class="container mx-auto">
            <h1 class="text-3xl font-bold mb-6">Welcome to CityPulse South Africa</h1>
            <p class="text-gray-600 mb-8">Discover the best local deals and events across South Africa.</p>
            
            <!-- Content Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Card 1 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="h-48 bg-gray-300"></div>
                    <div class="p-4">
                        <h3 class="font-bold text-lg mb-2">50% Off at Restaurant Name</h3>
                        <p class="text-gray-600 text-sm mb-3">Valid until Dec 31, 2023</p>
                        <div class="flex justify-between items-center">
                            <span class="text-blue-600 font-medium">Cape Town</span>
                            <button class="text-gray-500 hover:text-red-500">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- More cards... -->
            </div>
        </div>
    </div>

    <!-- Search Dialog -->
    <div class="search-dialog">
        <div class="search-dialog-content p-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold">Search & Filter</h2>
                <button class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
            
            <div class="relative mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                </svg>
                <input type="text" placeholder="Search deals, events or locations..." class="pl-10 pr-10 py-2 w-full border border-gray-300 rounded-md">
            </div>
            
            <div class="mb-4">
                <div class="grid grid-cols-3 gap-2 mb-4">
                    <button class="py-2 bg-blue-600 text-white rounded-md">All</button>
                    <button class="py-2 bg-gray-100 text-gray-700 rounded-md">Deals</button>
                    <button class="py-2 bg-gray-100 text-gray-700 rounded-md">Events</button>
                </div>
                
                <h3 class="font-medium mb-2">Categories</h3>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-2 mb-4">
                    <div>
                        <h4 class="text-xs text-gray-500 mb-1">Food & Drink</h4>
                        <button class="flex items-center w-full px-2 py-1.5 text-sm rounded-md hover:bg-gray-100 text-left">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-2 opacity-70" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                            </svg>
                            <span class="truncate">Restaurants</span>
                        </button>
                        <!-- More categories... -->
                    </div>
                    <!-- More category groups... -->
                </div>
                
                <h3 class="font-medium mb-2">Distance</h3>
                <div class="flex flex-wrap gap-2 mb-4">
                    <button class="flex items-center px-3 py-1.5 text-sm rounded-md border border-gray-200 hover:bg-gray-50">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 opacity-70" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                        </svg>
                        <span>Within 1km</span>
                    </button>
                    <!-- More distance options... -->
                </div>
                
                <!-- More filter sections... -->
            </div>
            
            <div class="flex justify-between">
                <button class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 inline" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                    </svg>
                    Reset Filters (3)
                </button>
                <button class="px-3 py-2 bg-blue-600 text-white rounded-md text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 inline" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                    </svg>
                    Search
                </button>
            </div>
        </div>
    </div>
</body>
</html>
